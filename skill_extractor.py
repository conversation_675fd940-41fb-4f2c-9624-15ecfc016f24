#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爆枪SWF技能数据提取器
自动解析binaryData目录中的所有技能文件，提取正面技能数据
排除boss、首领、秘境boss等负面技能
"""

import os
import xml.etree.ElementTree as ET
import json
import re
from typing import Dict, List, Set

class SkillExtractor:
    def __init__(self, binary_data_path: str):
        self.binary_data_path = binary_data_path
        self.skills = {}
        self.boss_keywords = {
            'boss', 'Boss', 'BOSS', '首领', '秘境', '暴君', '僵尸王', 
            '巫尸', '骑士', '狂战狼', '掘金尸', '精英', 'bossEdit',
            'BossEdit', 'enemy', 'Enemy', '怪物', '敌人'
        }
        # 包含所有发现的技能相关文件
        self.positive_skill_files = [
            'heroSkillClass', 'petSkillClass', 'armsSkillClass', 
            'vehicleSkillClass', 'equipSkillClass', 'outfitSkillClass',
            'skillClass', 'weaponSkillClass', 'unionSkillClass',
            'ninetySkillClass', 'partsSkillClass', 'wilderEnemySkillClass',
            'bcardSkillListClass', 'deviceSkillClass', 'peakSkillClass',
            'yingSkillClass', 'loveSkillWenJieClass', 'SpiderKingSkillClass',
            'skillOtherConditionClass', 'nightmareSkillClass', 'loveSkillXiaoMeiClass',
            'loveSkillZangShiClass', 'FightPigSkillClass', 'wenJieSkillClass',
            'loveSkillClass', 'xinLingSkillClass', 'loveSkillXinLingClass',
            'FightKingSkillClass', 'bcardRareSkillListClass', 'sceneSkillClass',
            'demonSkillClass'
        ]
        
        # 需要排除的boss相关技能文件
        self.boss_skill_files = [
            'enemySkillClass', 'bossEditSkillListClass'
        ]
        
    def is_boss_related(self, skill_name: str, skill_cn_name: str, father_name: str) -> bool:
        """判断是否为boss相关的负面技能"""
        text_to_check = f"{skill_name} {skill_cn_name} {father_name}".lower()
        
        # 检查boss关键词
        for keyword in self.boss_keywords:
            if keyword.lower() in text_to_check:
                return True
                
        # 检查特定的负面技能模式
        negative_patterns = [
            r'enemy', r'怪物', r'敌人', r'boss', r'首领', r'秘境',
            r'暴君', r'僵尸王', r'巫尸', r'骑士', r'狂战狼', r'掘金尸',
            r'精英', r'钢铁', r'飓风', r'无疆', r'尸狼'
        ]
        
        for pattern in negative_patterns:
            if re.search(pattern, text_to_check, re.IGNORECASE):
                return True
                
        return False
    
    def clean_xml_content(self, content: str) -> str:
        """清理XML内容，移除可能导致解析错误的字符"""
        import re
        
        # 移除无效的控制字符
        content = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', content)
        
        # 修复常见的XML格式问题
        content = re.sub(r'<!--[^>]*-->', '', content)  # 移除注释
        content = re.sub(r'<\?[^>]*\?>', '<?xml version="1.0" encoding="utf-8"?>', content, count=1)  # 标准化XML声明
        
        # 修复可能的标签问题
        content = re.sub(r'<([^>]+)>\s*<!--[^>]*-->\s*', r'<\1>', content)
        
        # 确保根元素存在
        if '<data>' not in content:
            content = content.replace('<?xml version="1.0" encoding="utf-8"?>', 
                                    '<?xml version="1.0" encoding="utf-8"?>\n<data>') + '</data>'
        
        return content
    
    def extract_skill_info(self, skill_element, father_name: str) -> Dict:
        """从技能XML元素中提取信息"""
        skill_info = {
            'name': skill_element.get('name', ''),
            'cnName': skill_element.get('cnName', ''),
            'father': father_name,
            'index': skill_element.get('index', ''),
            'description': '',
            'cd': '',
            'effectType': '',
            'conditionType': '',
            'target': '',
            'duration': '',
            'mul': '',
            'range': '',
            'iconUrl': ''
        }
        
        # 提取子元素信息
        for child in skill_element:
            if child.tag == 'name' and not skill_info['name']:
                skill_info['name'] = child.text or ''
            elif child.tag == 'cnName' and not skill_info['cnName']:
                skill_info['cnName'] = child.text or ''
            elif child.tag == 'description':
                skill_info['description'] = child.text or ''
            elif child.tag == 'cd':
                skill_info['cd'] = child.text or ''
            elif child.tag == 'effectType':
                skill_info['effectType'] = child.text or ''
            elif child.tag == 'conditionType':
                skill_info['conditionType'] = child.text or ''
            elif child.tag == 'target':
                skill_info['target'] = child.text or ''
            elif child.tag == 'duration':
                skill_info['duration'] = child.text or ''
            elif child.tag == 'mul':
                skill_info['mul'] = child.text or ''
            elif child.tag == 'range':
                skill_info['range'] = child.text or ''
            elif child.tag == 'iconUrl':
                skill_info['iconUrl'] = child.text or ''
                
        return skill_info
    
    def parse_skill_file(self, file_path: str) -> List[Dict]:
        """解析单个技能文件"""
        skills = []
        
        try:
            # 尝试多种编码解析XML文件
            content = None
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'latin-1']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    break
                except UnicodeDecodeError:
                    continue
            
            if content is None:
                print(f"无法解码文件: {file_path}")
                return skills
            
            # 清理XML内容，移除可能导致解析错误的字符
            content = self.clean_xml_content(content)
            
            # 解析XML
            root = ET.fromstring(content)
            
            # 遍历所有father节点
            for father in root.findall('father'):
                father_name = father.get('name', '')
                father_cn_name = father.get('cnName', '')
                
                # 跳过明显的boss相关father
                if self.is_boss_related(father_name, father_cn_name, ''):
                    print(f"跳过boss相关father: {father_name} - {father_cn_name}")
                    continue
                
                # 遍历所有skill节点
                for skill in father.findall('skill'):
                    skill_info = self.extract_skill_info(skill, father_name)
                    
                    # 检查是否为boss相关技能
                    if not self.is_boss_related(skill_info['name'], skill_info['cnName'], father_name):
                        if skill_info['name'] or skill_info['cnName']:  # 确保技能有名称
                            skills.append(skill_info)
                    else:
                        print(f"跳过boss技能: {skill_info['name']} - {skill_info['cnName']}")
                        
        except ET.ParseError as e:
            print(f"XML解析错误 {file_path}: {e}")
        except Exception as e:
            print(f"处理文件错误 {file_path}: {e}")
            
        return skills
    
    def extract_all_skills(self):
        """提取所有技能数据"""
        print("开始提取技能数据...")
        
        # 获取所有.bin文件
        for filename in os.listdir(self.binary_data_path):
            if not filename.endswith('.bin'):
                continue
                
            # 检查是否为技能相关文件
            is_skill_file = False
            for skill_type in self.positive_skill_files:
                if skill_type in filename:
                    is_skill_file = True
                    break
                    
            if not is_skill_file:
                continue
                
            file_path = os.path.join(self.binary_data_path, filename)
            print(f"处理文件: {filename}")
            
            skills = self.parse_skill_file(file_path)
            
            if skills:
                skill_type = filename.replace('_XMLOut_', '').replace('.bin', '')
                # 移除数字前缀
                skill_type = re.sub(r'^\d+_', '', skill_type)
                
                if skill_type not in self.skills:
                    self.skills[skill_type] = []
                self.skills[skill_type].extend(skills)
                
        print(f"技能提取完成！共找到 {sum(len(skills) for skills in self.skills.values())} 个技能")
    
    def generate_report(self) -> str:
        """生成技能报告"""
        report = []
        report.append("# 🎮 爆枪SWF完整技能数据库")
        report.append("## （已排除boss、首领、秘境boss等负面技能）")
        report.append("")
        
        total_skills = 0
        
        for skill_type, skills in self.skills.items():
            if not skills:
                continue
                
            report.append(f"### 📋 {skill_type}")
            report.append(f"**技能数量: {len(skills)}**")
            report.append("")
            
            for i, skill in enumerate(skills, 1):
                name = skill['cnName'] or skill['name']
                if not name:
                    continue
                    
                report.append(f"**{i}. {name}**")
                
                if skill['name'] and skill['name'] != skill['cnName']:
                    report.append(f"   - 英文名: `{skill['name']}`")
                
                if skill['description']:
                    report.append(f"   - 描述: {skill['description']}")
                
                details = []
                if skill['cd']:
                    details.append(f"冷却: {skill['cd']}秒")
                if skill['effectType']:
                    details.append(f"效果类型: {skill['effectType']}")
                if skill['conditionType']:
                    details.append(f"触发条件: {skill['conditionType']}")
                if skill['target']:
                    details.append(f"目标: {skill['target']}")
                if skill['duration']:
                    details.append(f"持续时间: {skill['duration']}")
                if skill['mul']:
                    details.append(f"倍数: {skill['mul']}")
                if skill['range']:
                    details.append(f"范围: {skill['range']}")
                    
                if details:
                    report.append(f"   - 属性: {' | '.join(details)}")
                
                report.append("")
            
            total_skills += len(skills)
            report.append("---")
            report.append("")
        
        # 添加统计信息
        report.insert(2, f"**总技能数量: {total_skills}**")
        report.insert(3, f"**技能类别: {len(self.skills)}**")
        report.insert(4, "")
        
        return "\n".join(report)
    
    def save_to_json(self, output_path: str):
        """保存为JSON格式"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.skills, f, ensure_ascii=False, indent=2)
        print(f"技能数据已保存到: {output_path}")
    
    def save_report(self, output_path: str):
        """保存技能报告"""
        report = self.generate_report()
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"技能报告已保存到: {output_path}")

def main():
    # 设置路径
    binary_data_path = r"D:\1\测试\爆枪swf测试\后台功能制作\binaryData"
    
    # 创建提取器
    extractor = SkillExtractor(binary_data_path)
    
    # 提取技能
    extractor.extract_all_skills()
    
    # 保存结果
    extractor.save_to_json("skills_data.json")
    extractor.save_report("skills_report.md")
    
    print("\n✅ 技能提取完成！")
    print("📄 详细报告: skills_report.md")
    print("📊 JSON数据: skills_data.json")

if __name__ == "__main__":
    main()
